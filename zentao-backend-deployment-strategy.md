# zentao-mcp-backend-service 详细环境部署策略

## 🎯 环境策略总览

| 环境 | 数据库 | 容器引擎 | 服务发现 | 监控 | 备份策略 |
|------|--------|----------|----------|------|----------|
| **开发** | SQLite | Docker/Podman | 本地 | 基础日志 | 手动 |
| **测试** | SQLite | Docker/Podman | 容器网络 | 详细日志 | 自动快照 |
| **生产** | PostgreSQL | Docker/Podman | 负载均衡 | 全面监控 | 定时备份 |

## 📁 目录结构设计

```
zentao-mcp-backend-service/
├── config/
│   ├── environments/
│   │   ├── dev.env                    # 开发环境配置
│   │   ├── test.env                   # 测试环境配置
│   │   └── prod.env                   # 生产环境配置
│   ├── docker/
│   │   ├── Dockerfile.dev             # 开发环境Docker
│   │   ├── Dockerfile.test            # 测试环境Docker
│   │   └── Dockerfile.prod            # 生产环境Docker
│   └── compose/
│       ├── docker-compose.dev.yml     # 开发环境编排
│       ├── docker-compose.test.yml    # 测试环境编排
│       └── docker-compose.prod.yml    # 生产环境编排
├── scripts/
│   ├── deploy-backend.sh              # 统一部署脚本(支持Docker/Podman)
│   ├── init-environment.sh            # 环境初始化脚本
│   ├── health-check.sh               # 健康检查脚本
│   └── backup-restore.sh             # 备份恢复脚本
└── monitoring/
    ├── prometheus.yml                 # Prometheus配置
    ├── grafana-dashboard.json         # Grafana仪表板
    └── alerts.yml                     # 告警规则
```

## 🔧 开发环境策略

### 环境特点
- **快速启动**：最小化依赖，快速开发迭代
- **调试友好**：详细日志，热重载支持
- **本地优先**：SQLite数据库，本地文件存储

### 配置文件 (config/environments/dev.env)
```bash
# 应用基础配置
APP_NAME=zentao-mcp-backend
APP_VERSION=dev
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp_dev.db
DATABASE_ECHO=true
DATABASE_POOL_RECYCLE=3600

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=1
RELOAD=true

# 安全配置
SECRET_KEY=dev-secret-key-$(date +%s)
DEBUG=true
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000

# 日志配置
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed
LOG_FILE=./logs/dev.log

# 禅道API配置
ZENTAO_ENV=beta
ZENTAO_BASE_URL=
ZENTAO_TIMEOUT=30
ZENTAO_RETRY_TIMES=3
ZENTAO_CACHE_TTL=300

# 开发工具配置
ENABLE_DOCS=true
ENABLE_PROFILER=true
ENABLE_DEBUG_TOOLBAR=true

# 容器配置
CONTAINER_NAME=zentao-backend-dev
NETWORK_NAME=zentao-dev-network
```

### Docker配置 (config/docker/Dockerfile.dev)
```dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN pip install uv && uv sync --dev

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs

# 设置权限
RUN chmod +x scripts/*.sh

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

### Compose配置 (config/compose/docker-compose.dev.yml)
```yaml
version: '3.8'

services:
  backend:
    build:
      context: ../../
      dockerfile: config/docker/Dockerfile.dev
    container_name: ${CONTAINER_NAME:-zentao-backend-dev}
    env_file:
      - ../environments/dev.env
    ports:
      - "8000:8000"
    volumes:
      - ../../:/app:cached
      - backend_data:/app/data
      - backend_logs:/app/logs
    networks:
      - zentao-dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  zentao-dev:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-dev-network}

volumes:
  backend_data:
    driver: local
  backend_logs:
    driver: local
```

## 🧪 测试环境策略

### 环境特点
- **生产模拟**：接近生产环境的配置
- **自动化测试**：支持CI/CD集成
- **数据隔离**：独立的测试数据

### 配置文件 (config/environments/test.env)
```bash
# 应用基础配置
APP_NAME=zentao-mcp-backend
APP_VERSION=test
ENVIRONMENT=testing

# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp_test.db
DATABASE_ECHO=false
DATABASE_POOL_RECYCLE=1800

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=2
RELOAD=false

# 安全配置
SECRET_KEY=${TEST_SECRET_KEY:-test-secret-key-change-me}
DEBUG=false
CORS_ORIGINS=${TEST_CORS_ORIGINS:-http://test.example.com}

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=./logs/test.log

# 禅道API配置
ZENTAO_ENV=beta
ZENTAO_BASE_URL=${TEST_ZENTAO_URL:-}
ZENTAO_TIMEOUT=20
ZENTAO_RETRY_TIMES=2
ZENTAO_CACHE_TTL=600

# 测试配置
ENABLE_DOCS=true
ENABLE_PROFILER=false
ENABLE_DEBUG_TOOLBAR=false
TEST_DATABASE_URL=sqlite:///./data/test_zentao_mcp.db

# 容器配置
CONTAINER_NAME=zentao-backend-test
NETWORK_NAME=zentao-test-network
```

## 🚀 生产环境策略

### 环境特点
- **高可用性**：多实例部署，负载均衡
- **安全优先**：最小权限，加密通信
- **性能优化**：连接池，缓存策略

### 配置文件 (config/environments/prod.env)
```bash
# 应用基础配置
APP_NAME=zentao-mcp-backend
APP_VERSION=${APP_VERSION:-latest}
ENVIRONMENT=production

# 数据库配置
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT:-5432}/${DB_NAME}
DATABASE_ECHO=false
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_RECYCLE=3600

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=${WORKERS:-4}
RELOAD=false

# 安全配置
SECRET_KEY=${SECRET_KEY}
DEBUG=false
CORS_ORIGINS=${CORS_ORIGINS}

# 日志配置
LOG_LEVEL=${LOG_LEVEL:-WARNING}
LOG_FORMAT=json
LOG_FILE=/var/log/zentao-mcp/app.log

# 禅道API配置
ZENTAO_ENV=online
ZENTAO_BASE_URL=${ZENTAO_BASE_URL}
ZENTAO_TIMEOUT=10
ZENTAO_RETRY_TIMES=1
ZENTAO_CACHE_TTL=1800

# 生产配置
ENABLE_DOCS=false
ENABLE_PROFILER=false
ENABLE_DEBUG_TOOLBAR=false

# 监控配置
SENTRY_DSN=${SENTRY_DSN}
PROMETHEUS_METRICS=true
HEALTH_CHECK_INTERVAL=30

# 容器配置
CONTAINER_NAME=zentao-backend-prod
NETWORK_NAME=zentao-prod-network
REPLICAS=${REPLICAS:-2}
```

## 🛠️ 统一部署脚本 (scripts/deploy-backend.sh)

```bash
#!/bin/bash
set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/config"

# 默认参数
ENVIRONMENT="dev"
ACTION="deploy"
CONTAINER_ENGINE=""
FORCE_REBUILD=false
VERBOSE=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Zentao MCP Backend 部署脚本

用法: $0 [选项] <环境> [动作]

环境:
  dev       开发环境 (默认)
  test      测试环境
  prod      生产环境

动作:
  deploy    部署服务 (默认)
  build     仅构建镜像
  start     启动服务
  stop      停止服务
  restart   重启服务
  logs      查看日志
  status    查看状态
  clean     清理环境

选项:
  -e, --engine ENGINE    指定容器引擎 (docker|podman)
  -f, --force           强制重新构建
  -v, --verbose         详细输出
  -h, --help            显示帮助信息

示例:
  $0 dev deploy                    # 部署开发环境
  $0 prod deploy --engine=podman   # 使用Podman部署生产环境
  $0 test build --force            # 强制重新构建测试环境
EOF
}

# 检测容器引擎
detect_container_engine() {
    if [[ -n "$CONTAINER_ENGINE" ]]; then
        return 0
    fi
    
    if command -v podman &> /dev/null; then
        CONTAINER_ENGINE="podman"
        log_info "检测到 Podman"
    elif command -v docker &> /dev/null; then
        CONTAINER_ENGINE="docker"
        log_info "检测到 Docker"
    else
        log_error "未找到容器引擎 (Docker 或 Podman)"
        exit 1
    fi
}

# 设置compose命令
setup_compose_command() {
    if [[ "$CONTAINER_ENGINE" == "podman" ]]; then
        if command -v podman-compose &> /dev/null; then
            COMPOSE_CMD="podman-compose"
        else
            log_error "Podman环境需要安装 podman-compose"
            exit 1
        fi
    else
        COMPOSE_CMD="docker-compose"
    fi
}

# 验证环境
validate_environment() {
    local env=$1
    local env_file="$CONFIG_DIR/environments/${env}.env"
    local compose_file="$CONFIG_DIR/compose/docker-compose.${env}.yml"
    
    if [[ ! -f "$env_file" ]]; then
        log_error "环境配置文件不存在: $env_file"
        exit 1
    fi
    
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose配置文件不存在: $compose_file"
        exit 1
    fi
    
    log_success "环境验证通过: $env"
}

# 执行部署
execute_deploy() {
    local env=$1
    local env_file="$CONFIG_DIR/environments/${env}.env"
    local compose_file="$CONFIG_DIR/compose/docker-compose.${env}.yml"
    
    log_info "开始部署 $env 环境..."
    
    # 创建网络（Podman需要）
    if [[ "$CONTAINER_ENGINE" == "podman" ]]; then
        local network_name=$(grep "NETWORK_NAME" "$env_file" | cut -d'=' -f2 | tr -d '"')
        podman network exists "$network_name" || podman network create "$network_name"
    fi
    
    # 执行部署
    $COMPOSE_CMD -f "$compose_file" --env-file "$env_file" up -d
    
    log_success "$env 环境部署完成"
}

# 主函数
main() {
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--engine)
                CONTAINER_ENGINE="$2"
                shift 2
                ;;
            --engine=*)
                CONTAINER_ENGINE="${1#*=}"
                shift
                ;;
            -f|--force)
                FORCE_REBUILD=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            dev|test|prod)
                ENVIRONMENT="$1"
                shift
                ;;
            deploy|build|start|stop|restart|logs|status|clean)
                ACTION="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检测和设置容器引擎
    detect_container_engine
    setup_compose_command
    
    # 验证环境
    validate_environment "$ENVIRONMENT"
    
    # 执行动作
    case $ACTION in
        deploy)
            execute_deploy "$ENVIRONMENT"
            ;;
        build)
            log_info "构建 $ENVIRONMENT 环境镜像..."
            # 构建逻辑
            ;;
        *)
            log_info "执行 $ACTION 动作 (环境: $ENVIRONMENT)"
            # 其他动作逻辑
            ;;
    esac
}

# 执行主函数
main "$@"
```
