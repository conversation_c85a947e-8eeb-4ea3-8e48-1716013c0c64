# zentao-mcp-client 部署配置分析报告

## 📋 项目概况

### 🎯 项目特点
- **类型**: Python CLI应用 + MCP代理服务
- **依赖管理**: uv + pyproject.toml
- **配置方式**: INI配置文件 + 环境变量
- **运行模式**: STDIO/HTTP/SSE三种模式
- **部署方式**: pip安装 / 源码运行 / 可执行文件

### 📁 目录结构
```
zentao-mcp-client/
├── zentao_mcp_client/          # 核心代码
│   ├── cli.py                  # 命令行接口
│   ├── config.py               # 配置管理
│   ├── proxy.py                # 代理服务核心
│   └── __main__.py             # 主入口
├── config.example.ini          # 配置示例
├── config.ini                  # 实际配置（包含API Key）
├── build_executable.py         # 可执行文件构建
├── publish_to_pypi.py          # PyPI发布脚本
├── run_tests.py               # 测试运行器
└── tests/                     # 测试文件
```

## ⚙️ 配置管理分析

### 🔧 配置文件层次
1. **环境变量** (最高优先级)
   - `ZENTAO_MCP_BACKEND_URL`
   - `ZENTAO_MCP_API_KEY`

2. **用户配置** (中等优先级)
   - `~/.zentao_mcp_client/config.ini`

3. **项目配置** (最低优先级)
   - `./config.ini` (开发环境回退)

### 📝 配置文件格式
```ini
[client]
backend_url = http://localhost:8000
api_key = your_secret_api_key_here
```

## 🚀 运行模式分析

### 1. STDIO模式 (默认)
- **用途**: 作为MCP客户端，通过标准输入输出与AI工具通信
- **启动**: `zentao-mcp-client start`
- **特点**: 无需网络端口，直接进程通信

### 2. HTTP模式
- **用途**: 作为HTTP代理服务器
- **启动**: `zentao-mcp-client start --mode http --host 0.0.0.0 --port 8080`
- **特点**: 提供REST API接口，可被其他服务调用

### 3. SSE模式
- **用途**: 服务器发送事件模式
- **启动**: `zentao-mcp-client start --mode sse --port 8080`
- **特点**: 支持实时事件推送

## 📦 部署方式分析

### 方式一: pip安装 (推荐生产环境)
```bash
pip install zentao-mcp-client
zentao-mcp-client configure
zentao-mcp-client start
```

### 方式二: 源码运行 (开发环境)
```bash
cd zentao-mcp-client
uv sync
uv run python -m zentao_mcp_client configure
uv run python -m zentao_mcp_client start
```

### 方式三: 可执行文件 (独立部署)
```bash
python build_executable.py
./dist/zentao-mcp-client configure
./dist/zentao-mcp-client start
```

## 🎯 部署脚本设计建议

### 环境分类
- **dev**: 开发环境 - 源码运行，热重载
- **test**: 测试环境 - pip安装，配置验证
- **prod**: 生产环境 - 可执行文件/系统服务

### 配置策略
- **开发环境**: 使用项目内config.ini，支持快速修改
- **测试环境**: 使用环境变量，便于CI/CD
- **生产环境**: 使用用户配置目录，支持多用户

### 服务管理
- **开发环境**: 直接运行，手动管理
- **测试环境**: 容器化运行，自动重启
- **生产环境**: 系统服务，开机自启

## 🔧 部署脚本功能需求

### 核心功能
1. **环境检查**: Python版本、依赖安装
2. **配置管理**: 自动创建配置文件，环境变量设置
3. **服务启动**: 支持三种运行模式
4. **健康检查**: 连接测试，服务状态监控
5. **日志管理**: 日志轮转，错误监控

### 高级功能
1. **可执行文件构建**: 自动化打包流程
2. **系统服务安装**: systemd服务配置
3. **配置向导**: 交互式配置生成
4. **备份恢复**: 配置文件备份和恢复

## 📊 与其他项目的集成

### 依赖关系
```
zentao-mcp-client → zentao-mcp-backend-service
                 ↓
              禅道API服务
```

### 部署顺序
1. **zentao-mcp-backend-service** (后端服务)
2. **zentao-mcp-client** (客户端代理)
3. **AI工具集成** (Claude Desktop等)

### 网络配置
- 客户端需要访问后端服务 (HTTP)
- HTTP模式需要监听端口 (可选)
- 无需数据库连接

## 🚨 安全考虑

### 配置安全
- API Key加密存储
- 配置文件权限控制 (600)
- 环境变量优先级

### 网络安全
- HTTPS连接验证
- API Key认证
- 请求频率限制

### 运行安全
- 非root用户运行
- 资源使用限制
- 错误信息脱敏

## 🎯 推荐的部署配置

### 开发环境
```bash
# 配置文件: ./config/client.dev.ini
[client]
backend_url = http://localhost:8000
api_key = dev-api-key-for-testing
log_level = DEBUG
timeout = 30
```

### 测试环境
```bash
# 环境变量配置
ZENTAO_MCP_BACKEND_URL=http://test-backend:8000
ZENTAO_MCP_API_KEY=${TEST_API_KEY}
ZENTAO_MCP_LOG_LEVEL=INFO
```

### 生产环境
```bash
# 用户配置: ~/.zentao_mcp_client/config.ini
[client]
backend_url = https://api.yourdomain.com
api_key = ${PROD_API_KEY}
log_level = WARNING
timeout = 15
retry_times = 3
```

## 📋 部署脚本实现清单

### 必需功能
- [x] 环境检查 (Python, uv)
- [x] 依赖安装 (uv sync)
- [x] 配置文件创建
- [x] 服务启动/停止
- [x] 健康检查

### 可选功能
- [ ] 可执行文件构建
- [ ] 系统服务安装
- [ ] 配置加密
- [ ] 日志轮转
- [ ] 性能监控

### 集成功能
- [ ] 与后端服务联动
- [ ] 统一配置管理
- [ ] 集群部署支持

## 🎯 实施建议

基于以上分析，建议创建以下部署脚本：

1. **deploy-client-universal.sh** - 主部署脚本
2. **client-service-template** - systemd服务模板
3. **client-config-wizard.py** - 配置向导脚本

重点关注：
- 配置文件的安全管理
- 多种运行模式的支持
- 与后端服务的集成
- 生产环境的服务化部署
