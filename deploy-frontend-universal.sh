#!/bin/bash
set -euo pipefail

# ============================================================================
# Zentao Token Web 通用部署脚本
# 支持 dev/test/prod 三种环境
# 支持 Docker 和 Podman 容器引擎
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
FRONTEND_DIR="$PROJECT_ROOT/zentao-token-web"
CONFIG_DIR="$FRONTEND_DIR/config"

# 默认参数
ENVIRONMENT="dev"
ACTION="deploy"
CONTAINER_ENGINE=""
FORCE_REBUILD=false
VERBOSE=false
NO_CACHE=false
PULL_LATEST=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${PURPLE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
Zentao Token Web 通用部署脚本

用法: ./deploy-frontend-universal.sh [选项] <环境> [动作]

环境:
  dev       开发环境 (默认) - Vite开发服务器，热重载
  test      测试环境 - 静态构建 + Nginx容器
  prod      生产环境 - 优化构建 + Nginx + HTTPS

动作:
  deploy    构建并部署服务 (默认)
  build     仅构建项目
  start     启动已存在的服务
  stop      停止服务
  restart   重启服务
  logs      查看服务日志
  status    查看服务状态
  clean     清理环境（停止并删除容器、镜像、卷）
  test      运行项目测试

选项:
  -e, --engine ENGINE    指定容器引擎 (docker|podman)
  -f, --force           强制重新构建
  -v, --verbose         详细输出模式
  --no-cache            构建时不使用缓存
  --pull                构建前拉取最新基础镜像
  -h, --help            显示帮助信息

示例:
  ./deploy-frontend-universal.sh dev                    # 开发环境部署
  ./deploy-frontend-universal.sh prod --engine=podman   # 使用Podman部署生产环境
  ./deploy-frontend-universal.sh test build --force     # 强制重新构建测试环境
  ./deploy-frontend-universal.sh dev logs               # 查看开发环境日志
  ./deploy-frontend-universal.sh prod clean             # 清理生产环境

环境说明:
  - dev:  Vite开发服务器，热重载，开发工具启用
  - test: 静态构建，Nginx容器，测试配置
  - prod: 优化构建，Nginx + HTTPS，生产配置
EOF
}

# 检测容器引擎
detect_container_engine() {
    if [[ -n "$CONTAINER_ENGINE" ]]; then
        log_debug "使用指定的容器引擎: $CONTAINER_ENGINE"
        return 0
    fi
    
    if command -v podman &> /dev/null; then
        CONTAINER_ENGINE="podman"
        log_info "自动检测到 Podman 容器引擎"
    elif command -v docker &> /dev/null; then
        CONTAINER_ENGINE="docker"
        log_info "自动检测到 Docker 容器引擎"
    else
        log_error "未找到可用的容器引擎 (Docker 或 Podman)"
        log_error "请安装 Docker 或 Podman 后重试"
        exit 1
    fi
}

# 设置compose命令
setup_compose_command() {
    if [[ "$CONTAINER_ENGINE" == "podman" ]]; then
        if command -v podman-compose &> /dev/null; then
            COMPOSE_CMD="podman-compose"
            log_debug "使用 podman-compose"
        elif command -v docker-compose &> /dev/null; then
            COMPOSE_CMD="docker-compose"
            log_warning "使用 docker-compose 与 Podman（可能存在兼容性问题）"
        else
            log_error "Podman环境需要安装 podman-compose 或 docker-compose"
            log_error "安装命令: pip install podman-compose"
            exit 1
        fi
    else
        if command -v docker-compose &> /dev/null; then
            COMPOSE_CMD="docker-compose"
            log_debug "使用 docker-compose"
        else
            log_error "Docker环境需要安装 docker-compose"
            log_error "安装命令: https://docs.docker.com/compose/install/"
            exit 1
        fi
    fi
}

# 检查Node.js和包管理器
check_node_environment() {
    log_info "检查Node.js环境..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        log_error "请安装 Node.js: https://nodejs.org/"
        exit 1
    fi
    
    local node_version=$(node --version)
    log_debug "Node.js 版本: $node_version"
    
    # 检查包管理器（优先使用bun）
    if command -v bun &> /dev/null; then
        PACKAGE_MANAGER="bun"
        log_debug "使用 bun 作为包管理器"
    elif command -v pnpm &> /dev/null; then
        PACKAGE_MANAGER="pnpm"
        log_debug "使用 pnpm 作为包管理器"
    elif command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        log_debug "使用 yarn 作为包管理器"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        log_debug "使用 npm 作为包管理器"
    else
        log_error "未找到可用的包管理器 (bun/pnpm/yarn/npm)"
        exit 1
    fi
}

# 验证环境配置
validate_environment() {
    local env=$1
    
    log_info "验证 $env 环境配置..."
    
    # 检查前端项目目录
    if [[ ! -d "$FRONTEND_DIR" ]]; then
        log_error "前端项目目录不存在: $FRONTEND_DIR"
        exit 1
    fi
    
    # 检查package.json
    if [[ ! -f "$FRONTEND_DIR/package.json" ]]; then
        log_error "package.json 不存在: $FRONTEND_DIR/package.json"
        exit 1
    fi
    
    # 检查配置目录
    if [[ ! -d "$CONFIG_DIR" ]]; then
        log_info "创建配置目录: $CONFIG_DIR"
        mkdir -p "$CONFIG_DIR"
    fi
    
    # 检查环境配置文件
    local env_file="$CONFIG_DIR/.env.${env}"
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境配置文件不存在: $env_file"
        log_info "创建默认配置文件..."
        create_default_env_file "$env"
    fi
    
    # 检查Dockerfile
    local dockerfile="$CONFIG_DIR/Dockerfile.${env}"
    if [[ ! -f "$dockerfile" ]] && [[ "$env" != "dev" ]]; then
        log_warning "Dockerfile不存在: $dockerfile"
        log_info "创建默认Dockerfile..."
        create_default_dockerfile "$env"
    fi
    
    # 检查docker-compose文件
    local compose_file="$CONFIG_DIR/docker-compose.${env}.yml"
    if [[ ! -f "$compose_file" ]] && [[ "$env" != "dev" ]]; then
        log_warning "Compose配置文件不存在: $compose_file"
        log_info "创建默认Compose文件..."
        create_default_compose_file "$env"
    fi
    
    log_success "环境配置验证完成: $env"
}

# 创建默认环境配置文件
create_default_env_file() {
    local env=$1
    local env_file="$CONFIG_DIR/.env.${env}"
    
    case $env in
        dev)
            cat > "$env_file" << 'EOF'
# 开发环境配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_VERSION=v1
VITE_API_TIMEOUT=10000

# 开发配置
VITE_APP_ENV=development
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true

# 调试配置
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE=true

# 开发服务器配置
VITE_HOST=0.0.0.0
VITE_PORT=3000
VITE_OPEN=false
EOF
            ;;
        test)
            cat > "$env_file" << 'EOF'
# 测试环境配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_VERSION=v1
VITE_API_TIMEOUT=15000

# 测试配置
VITE_APP_ENV=testing
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 调试配置
VITE_LOG_LEVEL=info
VITE_ENABLE_CONSOLE=false

# 构建配置
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_MINIFY=true
EOF
            ;;
        prod)
            cat > "$env_file" << 'EOF'
# 生产环境配置
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_API_VERSION=v1
VITE_API_TIMEOUT=20000

# 生产配置
VITE_APP_ENV=production
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 性能配置
VITE_LOG_LEVEL=error
VITE_ENABLE_CONSOLE=false
VITE_ENABLE_PWA=true

# 构建配置
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
VITE_BUILD_GZIP=true
EOF
            ;;
    esac
    
    log_success "已创建默认环境配置: $env_file"
}

# 创建默认Dockerfile
create_default_dockerfile() {
    local env=$1
    local dockerfile="$CONFIG_DIR/Dockerfile.${env}"
    
    case $env in
        test|prod)
            cat > "$dockerfile" << 'EOF'
# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY bun.lockb* ./

# 安装依赖
RUN if [ -f "bun.lockb" ]; then \
        npm install -g bun && bun install; \
    else \
        npm ci --only=production; \
    fi

# 复制源代码
COPY . .

# 构建应用
RUN if [ -f "bun.lockb" ]; then \
        bun run build; \
    else \
        npm run build; \
    fi

# 生产阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY config/nginx.conf /etc/nginx/nginx.conf

# 创建非root用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# 切换到非root用户
USER nginx

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# 启动命令
CMD ["nginx", "-g", "daemon off;"]
EOF
            ;;
    esac
    
    log_success "已创建默认Dockerfile: $dockerfile"
}

# 创建默认Compose文件
create_default_compose_file() {
    local env=$1
    local compose_file="$CONFIG_DIR/docker-compose.${env}.yml"

    case $env in
        test)
            cat > "$compose_file" << 'EOF'
version: '3.8'

services:
  frontend:
    build:
      context: ../
      dockerfile: config/Dockerfile.test
    container_name: zentao-frontend-test
    ports:
      - "3000:8080"
    environment:
      - NODE_ENV=test
    networks:
      - zentao-test
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  zentao-test:
    driver: bridge
    name: zentao-test-network
EOF
            ;;
        prod)
            cat > "$compose_file" << 'EOF'
version: '3.8'

services:
  frontend:
    build:
      context: ../
      dockerfile: config/Dockerfile.prod
    container_name: zentao-frontend-prod
    ports:
      - "80:8080"
      - "443:8443"
    environment:
      - NODE_ENV=production
    networks:
      - zentao-prod
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  zentao-prod:
    driver: bridge
    name: zentao-prod-network
EOF
            ;;
    esac

    log_success "已创建默认Compose配置: $compose_file"
}

# 创建nginx配置
create_nginx_config() {
    local nginx_config="$CONFIG_DIR/nginx.conf"

    if [[ ! -f "$nginx_config" ]]; then
        cat > "$nginx_config" << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    server {
        listen 8080;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html index.htm;

        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # SPA路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # API代理
        location /api/ {
            proxy_pass http://backend:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF
        log_success "已创建nginx配置: $nginx_config"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装前端依赖..."

    cd "$FRONTEND_DIR"

    case $PACKAGE_MANAGER in
        bun)
            bun install
            ;;
        pnpm)
            pnpm install
            ;;
        yarn)
            yarn install
            ;;
        npm)
            npm install
            ;;
    esac

    log_success "依赖安装完成"
}

# 运行测试
run_tests() {
    local env=$1

    log_info "运行 $env 环境测试..."

    cd "$FRONTEND_DIR"

    # 类型检查
    if [[ -f "tsconfig.json" ]]; then
        log_info "执行TypeScript类型检查..."
        case $PACKAGE_MANAGER in
            bun)
                bun run type-check || log_warning "类型检查有警告"
                ;;
            *)
                $PACKAGE_MANAGER run type-check || log_warning "类型检查有警告"
                ;;
        esac
    fi

    # ESLint检查
    log_info "执行ESLint代码检查..."
    case $PACKAGE_MANAGER in
        bun)
            bun run lint || log_warning "代码检查有警告"
            ;;
        *)
            $PACKAGE_MANAGER run lint || log_warning "代码检查有警告"
            ;;
    esac

    log_success "测试完成"
}

# 构建项目
build_project() {
    local env=$1

    log_info "构建 $env 环境项目..."

    cd "$FRONTEND_DIR"

    # 复制环境配置文件
    local env_file="$CONFIG_DIR/.env.${env}"
    if [[ -f "$env_file" ]]; then
        cp "$env_file" ".env.local"
        log_debug "已复制环境配置文件"
    fi

    # 执行构建
    case $PACKAGE_MANAGER in
        bun)
            bun run build
            ;;
        *)
            $PACKAGE_MANAGER run build
            ;;
    esac

    # 检查构建结果
    if [[ -d "dist" ]]; then
        log_success "项目构建完成"
        log_info "构建文件大小:"
        du -sh dist/
    else
        log_error "构建失败，dist目录不存在"
        exit 1
    fi
}

# 开发环境启动
start_dev_server() {
    local env=$1

    log_info "启动 $env 环境开发服务器..."

    cd "$FRONTEND_DIR"

    # 复制环境配置文件
    local env_file="$CONFIG_DIR/.env.${env}"
    if [[ -f "$env_file" ]]; then
        cp "$env_file" ".env.local"
        log_debug "已复制环境配置文件"
    fi

    # 启动开发服务器
    case $PACKAGE_MANAGER in
        bun)
            bun run dev
            ;;
        *)
            $PACKAGE_MANAGER run dev
            ;;
    esac
}

# 容器化部署
deploy_with_container() {
    local env=$1

    log_info "使用容器部署 $env 环境..."

    cd "$FRONTEND_DIR"

    # 创建nginx配置
    create_nginx_config

    # 获取配置文件路径
    local compose_file="$CONFIG_DIR/docker-compose.${env}.yml"

    # 创建网络（Podman需要）
    if [[ "$CONTAINER_ENGINE" == "podman" ]]; then
        local network_name="zentao-${env}-network"
        if ! podman network exists "$network_name" 2>/dev/null; then
            log_info "创建Podman网络: $network_name"
            podman network create "$network_name"
        fi
    fi

    # 构建和启动服务
    local build_args=""
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi

    if [[ "$PULL_LATEST" == "true" ]]; then
        build_args="$build_args --pull"
    fi

    $COMPOSE_CMD -f "$compose_file" build $build_args
    $COMPOSE_CMD -f "$compose_file" up -d

    log_success "容器部署完成"
}

# 获取配置文件路径
get_config_paths() {
    local env=$1
    ENV_FILE="$CONFIG_DIR/.env.${env}"
    COMPOSE_FILE="$CONFIG_DIR/docker-compose.${env}.yml"
    DOCKERFILE="$CONFIG_DIR/Dockerfile.${env}"
}

# 启动服务
start_services() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        start_dev_server "$env"
    else
        log_info "启动 $env 环境容器服务..."
        cd "$FRONTEND_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" start
        log_success "服务启动完成"
    fi
}

# 停止服务
stop_services() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "开发环境请使用 Ctrl+C 停止开发服务器"
    else
        log_info "停止 $env 环境服务..."
        cd "$FRONTEND_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" stop
        log_success "服务停止完成"
    fi
}

# 重启服务
restart_services() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "开发环境请重新运行部署命令"
    else
        log_info "重启 $env 环境服务..."
        cd "$FRONTEND_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" restart
        log_success "服务重启完成"
    fi
}

# 查看服务状态
show_status() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "开发环境状态检查..."
        if curl -f -s http://localhost:3000 &> /dev/null; then
            log_success "开发服务器运行中 (http://localhost:3000)"
        else
            log_warning "开发服务器未运行"
        fi
    else
        log_info "查看 $env 环境服务状态..."
        cd "$FRONTEND_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" ps
    fi
}

# 查看日志
show_logs() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "开发环境日志请查看终端输出"
    else
        log_info "查看 $env 环境日志..."
        cd "$FRONTEND_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" logs -f
    fi
}

# 清理环境
clean_environment() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "清理开发环境..."
        cd "$FRONTEND_DIR"
        rm -rf node_modules/.cache dist .env.local
        log_success "开发环境清理完成"
    else
        log_warning "清理 $env 环境（这将删除所有容器和镜像）..."

        # 确认操作
        if [[ "$FORCE_REBUILD" != "true" ]]; then
            echo -n "确认清理 $env 环境？这将删除所有容器和镜像 [y/N]: "
            read -r confirm
            if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
                log_info "取消清理操作"
                return 0
            fi
        fi

        cd "$FRONTEND_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" down --volumes --remove-orphans || true

        # 删除镜像
        local image_name="zentao-frontend-${env}"
        if [[ "$CONTAINER_ENGINE" == "docker" ]]; then
            docker rmi "$image_name" 2>/dev/null || true
        else
            podman rmi "$image_name" 2>/dev/null || true
        fi

        log_success "环境清理完成"
    fi
}

# 主函数
main() {
    log_info "Zentao Token Web 部署脚本启动"

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--engine)
                CONTAINER_ENGINE="$2"
                shift 2
                ;;
            --engine=*)
                CONTAINER_ENGINE="${1#*=}"
                shift
                ;;
            -f|--force)
                FORCE_REBUILD=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --pull)
                PULL_LATEST=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            dev|test|prod)
                ENVIRONMENT="$1"
                shift
                ;;
            deploy|build|start|stop|restart|logs|status|clean|test)
                ACTION="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 验证环境参数
    if [[ ! "$ENVIRONMENT" =~ ^(dev|test|prod)$ ]]; then
        log_error "无效的环境: $ENVIRONMENT"
        log_error "支持的环境: dev, test, prod"
        exit 1
    fi

    # 检查Node.js环境
    check_node_environment

    # 对于非开发环境，检测容器引擎
    if [[ "$ENVIRONMENT" != "dev" ]] || [[ "$ACTION" =~ ^(deploy|build|start|stop|restart|logs|status|clean)$ ]]; then
        detect_container_engine
        setup_compose_command
    fi

    # 验证环境配置
    validate_environment "$ENVIRONMENT"

    # 获取配置文件路径
    get_config_paths "$ENVIRONMENT"

    log_info "使用配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  动作: $ACTION"
    log_info "  包管理器: $PACKAGE_MANAGER"
    if [[ "$ENVIRONMENT" != "dev" ]]; then
        log_info "  容器引擎: $CONTAINER_ENGINE"
        log_info "  配置文件: $ENV_FILE"
        log_info "  Compose文件: $COMPOSE_FILE"
    fi

    # 进入前端项目目录
    cd "$FRONTEND_DIR"

    # 执行对应的动作
    case $ACTION in
        deploy)
            install_dependencies
            if [[ "$ENVIRONMENT" == "dev" ]]; then
                log_info "开发环境部署 - 启动开发服务器"
                start_dev_server "$ENVIRONMENT"
            else
                build_project "$ENVIRONMENT"
                deploy_with_container "$ENVIRONMENT"
                show_status "$ENVIRONMENT"
            fi
            ;;
        build)
            install_dependencies
            if [[ "$ENVIRONMENT" == "dev" ]]; then
                log_info "开发环境构建 - 仅安装依赖"
            else
                build_project "$ENVIRONMENT"
            fi
            ;;
        start)
            start_services "$ENVIRONMENT"
            ;;
        stop)
            stop_services "$ENVIRONMENT"
            ;;
        restart)
            restart_services "$ENVIRONMENT"
            ;;
        status)
            show_status "$ENVIRONMENT"
            ;;
        logs)
            show_logs "$ENVIRONMENT"
            ;;
        test)
            install_dependencies
            run_tests "$ENVIRONMENT"
            ;;
        clean)
            clean_environment "$ENVIRONMENT"
            ;;
        *)
            log_error "未知动作: $ACTION"
            show_help
            exit 1
            ;;
    esac

    log_success "操作完成!"
}

# 错误处理
trap 'log_error "脚本执行失败，退出码: $?"' ERR

# 执行主函数
main "$@"
