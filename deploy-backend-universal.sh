#!/bin/bash
set -euo pipefail

# ============================================================================
# Zentao MCP Backend 通用部署脚本
# 支持 Docker 和 Podman 容器引擎
# 支持 dev/test/prod 三种环境
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
CONFIG_DIR="$PROJECT_ROOT/zentao-mcp-backend-service/config"

# 默认参数
ENVIRONMENT="dev"
ACTION="deploy"
CONTAINER_ENGINE=""
FORCE_REBUILD=false
VERBOSE=false
NO_CACHE=false
PULL_LATEST=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${PURPLE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
Zentao MCP Backend 通用部署脚本

用法: ./deploy-backend-universal.sh [选项] <环境> [动作]

环境:
  dev       开发环境 (默认) - SQLite数据库，开发配置
  test      测试环境 - SQLite数据库，测试配置
  prod      生产环境 - PostgreSQL数据库，生产配置

动作:
  deploy    构建并部署服务 (默认)
  build     仅构建镜像
  start     启动已存在的服务
  stop      停止服务
  restart   重启服务
  logs      查看服务日志
  status    查看服务状态
  clean     清理环境（停止并删除容器、镜像、卷）
  health    检查服务健康状态

选项:
  -e, --engine ENGINE    指定容器引擎 (docker|podman)
  -f, --force           强制重新构建镜像
  -v, --verbose         详细输出模式
  --no-cache            构建时不使用缓存
  --pull                构建前拉取最新基础镜像
  -h, --help            显示帮助信息

示例:
  ./deploy-backend-universal.sh dev                    # 部署开发环境
  ./deploy-backend-universal.sh prod --engine=podman   # 使用Podman部署生产环境
  ./deploy-backend-universal.sh test build --force     # 强制重新构建测试环境
  ./deploy-backend-universal.sh dev logs               # 查看开发环境日志
  ./deploy-backend-universal.sh prod clean             # 清理生产环境

环境说明:
  - dev:  SQLite数据库，热重载，详细日志，开发工具启用
  - test: SQLite数据库，生产模式，中等日志，测试配置
  - prod: PostgreSQL数据库，生产优化，错误日志，安全配置
EOF
}

# 检测容器引擎
detect_container_engine() {
    if [[ -n "$CONTAINER_ENGINE" ]]; then
        log_debug "使用指定的容器引擎: $CONTAINER_ENGINE"
        return 0
    fi
    
    if command -v podman &> /dev/null; then
        CONTAINER_ENGINE="podman"
        log_info "自动检测到 Podman 容器引擎"
    elif command -v docker &> /dev/null; then
        CONTAINER_ENGINE="docker"
        log_info "自动检测到 Docker 容器引擎"
    else
        log_error "未找到可用的容器引擎 (Docker 或 Podman)"
        log_error "请安装 Docker 或 Podman 后重试"
        exit 1
    fi
}

# 设置compose命令
setup_compose_command() {
    if [[ "$CONTAINER_ENGINE" == "podman" ]]; then
        if command -v podman-compose &> /dev/null; then
            COMPOSE_CMD="podman-compose"
            log_debug "使用 podman-compose"
        elif command -v docker-compose &> /dev/null; then
            COMPOSE_CMD="docker-compose"
            log_warning "使用 docker-compose 与 Podman（可能存在兼容性问题）"
        else
            log_error "Podman环境需要安装 podman-compose 或 docker-compose"
            log_error "安装命令: pip install podman-compose"
            exit 1
        fi
    else
        if command -v docker-compose &> /dev/null; then
            COMPOSE_CMD="docker-compose"
            log_debug "使用 docker-compose"
        else
            log_error "Docker环境需要安装 docker-compose"
            log_error "安装命令: https://docs.docker.com/compose/install/"
            exit 1
        fi
    fi
}

# 验证环境配置
validate_environment() {
    local env=$1
    
    log_info "验证 $env 环境配置..."
    
    # 检查配置目录
    if [[ ! -d "$CONFIG_DIR" ]]; then
        log_error "配置目录不存在: $CONFIG_DIR"
        log_error "请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查环境配置文件
    local env_file="$CONFIG_DIR/environments/${env}.env"
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境配置文件不存在: $env_file"
        log_info "创建默认配置文件..."
        create_default_env_file "$env"
    fi
    
    # 检查Compose配置文件
    local compose_file="$CONFIG_DIR/compose/docker-compose.${env}.yml"
    if [[ ! -f "$compose_file" ]]; then
        log_warning "Compose配置文件不存在: $compose_file"
        log_info "创建默认Compose文件..."
        create_default_compose_file "$env"
    fi
    
    # 检查Dockerfile
    local dockerfile="$CONFIG_DIR/docker/Dockerfile.${env}"
    if [[ ! -f "$dockerfile" ]]; then
        log_warning "Dockerfile不存在: $dockerfile"
        log_info "创建默认Dockerfile..."
        create_default_dockerfile "$env"
    fi
    
    log_success "环境配置验证完成: $env"
}

# 创建默认环境配置文件
create_default_env_file() {
    local env=$1
    local env_file="$CONFIG_DIR/environments/${env}.env"
    
    # 确保目录存在
    mkdir -p "$(dirname "$env_file")"
    
    case $env in
        dev)
            cat > "$env_file" << 'EOF'
# 开发环境配置
APP_NAME=zentao-mcp-backend
APP_VERSION=dev
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp_dev.db
DATABASE_ECHO=true

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=1
RELOAD=true

# 安全配置
SECRET_KEY=dev-secret-key-not-for-production
DEBUG=true
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# 日志配置
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed

# 禅道API配置
ZENTAO_ENV=beta
ZENTAO_TIMEOUT=30

# 开发工具配置
ENABLE_DOCS=true
ENABLE_PROFILER=true

# 容器配置
CONTAINER_NAME=zentao-backend-dev
NETWORK_NAME=zentao-dev-network
EOF
            ;;
        test)
            cat > "$env_file" << 'EOF'
# 测试环境配置
APP_NAME=zentao-mcp-backend
APP_VERSION=test
ENVIRONMENT=testing

# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp_test.db
DATABASE_ECHO=false

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=2
RELOAD=false

# 安全配置
SECRET_KEY=test-secret-key-change-in-production
DEBUG=false
CORS_ORIGINS=http://test.example.com

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 禅道API配置
ZENTAO_ENV=beta
ZENTAO_TIMEOUT=20

# 测试配置
ENABLE_DOCS=true
ENABLE_PROFILER=false

# 容器配置
CONTAINER_NAME=zentao-backend-test
NETWORK_NAME=zentao-test-network
EOF
            ;;
        prod)
            cat > "$env_file" << 'EOF'
# 生产环境配置
APP_NAME=zentao-mcp-backend
APP_VERSION=latest
ENVIRONMENT=production

# 数据库配置 - 需要设置实际的PostgreSQL连接信息
DATABASE_URL=************************************************/zentao_mcp_prod
DATABASE_ECHO=false
DATABASE_POOL_SIZE=20

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=false

# 安全配置 - 生产环境必须修改
SECRET_KEY=CHANGE_ME_IN_PRODUCTION
DEBUG=false
CORS_ORIGINS=https://yourdomain.com

# 日志配置
LOG_LEVEL=WARNING
LOG_FORMAT=json

# 禅道API配置
ZENTAO_ENV=online
ZENTAO_TIMEOUT=10

# 生产配置
ENABLE_DOCS=false
ENABLE_PROFILER=false

# 容器配置
CONTAINER_NAME=zentao-backend-prod
NETWORK_NAME=zentao-prod-network
REPLICAS=2
EOF
            ;;
    esac
    
    log_success "已创建默认环境配置: $env_file"
}

# 创建默认Compose文件
create_default_compose_file() {
    local env=$1
    local compose_file="$CONFIG_DIR/compose/docker-compose.${env}.yml"
    
    # 确保目录存在
    mkdir -p "$(dirname "$compose_file")"
    
    # 根据环境创建不同的compose文件
    case $env in
        dev|test)
            cat > "$compose_file" << EOF
version: '3.8'

services:
  backend:
    build:
      context: ../../../zentao-mcp-backend-service
      dockerfile: config/docker/Dockerfile.${env}
    container_name: \${CONTAINER_NAME:-zentao-backend-${env}}
    env_file:
      - ../environments/${env}.env
    ports:
      - "8000:8000"
    volumes:
      - backend_data:/app/data
      - backend_logs:/app/logs
    networks:
      - zentao-${env}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  zentao-${env}:
    driver: bridge
    name: \${NETWORK_NAME:-zentao-${env}-network}

volumes:
  backend_data:
    driver: local
  backend_logs:
    driver: local
EOF
            ;;
        prod)
            cat > "$compose_file" << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: zentao-postgres-prod
    environment:
      POSTGRES_DB: zentao_mcp_prod
      POSTGRES_USER: zentao_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-change_me}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - zentao-prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U zentao_user -d zentao_mcp_prod"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ../../../zentao-mcp-backend-service
      dockerfile: config/docker/Dockerfile.prod
    container_name: ${CONTAINER_NAME:-zentao-backend-prod}
    env_file:
      - ../environments/prod.env
    ports:
      - "8000:8000"
    volumes:
      - backend_logs:/app/logs
    networks:
      - zentao-prod
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: ${REPLICAS:-2}
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  zentao-prod:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-prod-network}

volumes:
  postgres_data:
    driver: local
  backend_logs:
    driver: local
EOF
            ;;
    esac
    
    log_success "已创建默认Compose配置: $compose_file"
}

# 创建默认Dockerfile
create_default_dockerfile() {
    local env=$1
    local dockerfile="$CONFIG_DIR/docker/Dockerfile.${env}"
    
    # 确保目录存在
    mkdir -p "$(dirname "$dockerfile")"
    
    case $env in
        dev)
            cat > "$dockerfile" << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN pip install uv && uv sync --dev

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
EOF
            ;;
        test|prod)
            cat > "$dockerfile" << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN pip install uv && uv sync --no-dev

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
            ;;
    esac
    
    log_success "已创建默认Dockerfile: $dockerfile"
}

# 获取配置文件路径
get_config_paths() {
    local env=$1
    ENV_FILE="$CONFIG_DIR/environments/${env}.env"
    COMPOSE_FILE="$CONFIG_DIR/compose/docker-compose.${env}.yml"
    DOCKERFILE="$CONFIG_DIR/docker/Dockerfile.${env}"
}

# 创建网络（Podman需要）
ensure_network() {
    local env=$1

    if [[ "$CONTAINER_ENGINE" == "podman" ]]; then
        local network_name
        if [[ -f "$ENV_FILE" ]]; then
            network_name=$(grep "^NETWORK_NAME=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"' | tr -d "'")
        fi
        network_name=${network_name:-"zentao-${env}-network"}

        if ! podman network exists "$network_name" 2>/dev/null; then
            log_info "创建Podman网络: $network_name"
            podman network create "$network_name"
        else
            log_debug "网络已存在: $network_name"
        fi
    fi
}

# 构建镜像
build_images() {
    local env=$1
    local build_args=""

    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi

    if [[ "$PULL_LATEST" == "true" ]]; then
        build_args="$build_args --pull"
    fi

    log_info "构建 $env 环境镜像..."

    if [[ "$VERBOSE" == "true" ]]; then
        $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build $build_args
    else
        $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build $build_args > /dev/null 2>&1
    fi

    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    local env=$1

    log_info "部署 $env 环境服务..."

    # 确保网络存在
    ensure_network "$env"

    # 启动服务
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d

    log_success "服务部署完成"

    # 等待服务就绪
    wait_for_services "$env"
}

# 等待服务就绪
wait_for_services() {
    local env=$1
    local max_attempts=30
    local attempt=1

    log_info "等待服务就绪..."

    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s http://localhost:8000/health &> /dev/null; then
            log_success "服务已就绪"
            return 0
        fi

        log_debug "等待服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done

    log_warning "服务启动超时，请检查日志"
    return 1
}

# 启动服务
start_services() {
    local env=$1

    log_info "启动 $env 环境服务..."

    ensure_network "$env"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" start

    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    local env=$1

    log_info "停止 $env 环境服务..."

    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" stop

    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    local env=$1

    log_info "重启 $env 环境服务..."

    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart

    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    local env=$1

    log_info "查看 $env 环境服务状态..."

    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
}

# 查看日志
show_logs() {
    local env=$1
    local service=${2:-}

    log_info "查看 $env 环境日志..."

    if [[ -n "$service" ]]; then
        $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f "$service"
    else
        $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
    fi
}

# 健康检查
health_check() {
    local env=$1

    log_info "执行 $env 环境健康检查..."

    # 检查服务状态
    local status_output
    status_output=$($COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps --format json 2>/dev/null || echo "[]")

    if [[ "$status_output" == "[]" ]]; then
        log_warning "没有运行的服务"
        return 1
    fi

    # 检查HTTP健康端点
    if curl -f -s http://localhost:8000/health &> /dev/null; then
        log_success "HTTP健康检查通过"
    else
        log_error "HTTP健康检查失败"
        return 1
    fi

    # 检查数据库连接（如果是生产环境）
    if [[ "$env" == "prod" ]]; then
        log_info "检查数据库连接..."
        # 这里可以添加数据库连接检查逻辑
    fi

    log_success "健康检查完成"
}

# 清理环境
clean_environment() {
    local env=$1

    log_warning "清理 $env 环境（这将删除所有数据）..."

    # 确认操作
    if [[ "$FORCE_REBUILD" != "true" ]]; then
        echo -n "确认清理 $env 环境？这将删除所有容器、镜像和数据卷 [y/N]: "
        read -r confirm
        if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
            log_info "取消清理操作"
            return 0
        fi
    fi

    log_info "停止并删除服务..."
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down --volumes --remove-orphans || true

    # 删除镜像
    local image_name="zentao-mcp-backend-${env}"
    if [[ "$CONTAINER_ENGINE" == "docker" ]]; then
        docker rmi "$image_name" 2>/dev/null || true
    else
        podman rmi "$image_name" 2>/dev/null || true
    fi

    log_success "环境清理完成"
}

# 主函数
main() {
    log_info "Zentao MCP Backend 部署脚本启动"

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--engine)
                CONTAINER_ENGINE="$2"
                shift 2
                ;;
            --engine=*)
                CONTAINER_ENGINE="${1#*=}"
                shift
                ;;
            -f|--force)
                FORCE_REBUILD=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --pull)
                PULL_LATEST=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            dev|test|prod)
                ENVIRONMENT="$1"
                shift
                ;;
            deploy|build|start|stop|restart|logs|status|clean|health)
                ACTION="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 验证环境参数
    if [[ ! "$ENVIRONMENT" =~ ^(dev|test|prod)$ ]]; then
        log_error "无效的环境: $ENVIRONMENT"
        log_error "支持的环境: dev, test, prod"
        exit 1
    fi

    # 检测和设置容器引擎
    detect_container_engine
    setup_compose_command

    # 验证环境配置
    validate_environment "$ENVIRONMENT"

    # 获取配置文件路径
    get_config_paths "$ENVIRONMENT"

    log_info "使用配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  容器引擎: $CONTAINER_ENGINE"
    log_info "  动作: $ACTION"
    log_info "  配置文件: $ENV_FILE"
    log_info "  Compose文件: $COMPOSE_FILE"

    # 执行对应的动作
    case $ACTION in
        deploy)
            build_images "$ENVIRONMENT"
            deploy_services "$ENVIRONMENT"
            show_status "$ENVIRONMENT"
            ;;
        build)
            build_images "$ENVIRONMENT"
            ;;
        start)
            start_services "$ENVIRONMENT"
            ;;
        stop)
            stop_services "$ENVIRONMENT"
            ;;
        restart)
            restart_services "$ENVIRONMENT"
            ;;
        status)
            show_status "$ENVIRONMENT"
            ;;
        logs)
            show_logs "$ENVIRONMENT"
            ;;
        health)
            health_check "$ENVIRONMENT"
            ;;
        clean)
            clean_environment "$ENVIRONMENT"
            ;;
        *)
            log_error "未知动作: $ACTION"
            show_help
            exit 1
            ;;
    esac

    log_success "操作完成!"
}

# 错误处理
trap 'log_error "脚本执行失败，退出码: $?"' ERR

# 执行主函数
main "$@"
