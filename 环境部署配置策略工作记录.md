# 环境部署配置策略工作记录

创建时间：2025-09-05
评估结果：高理解深度 + 系统变更 + 中风险

## 📋 四个项目部署策略建议

### 1. zentao-mcp-backend-service (核心后端服务)

#### 🎯 推荐部署策略
- **开发环境**: SQLite + 本地开发服务器
- **测试环境**: SQLite + Docker容器
- **生产环境**: PostgreSQL + Docker Swarm/K8s

#### ⚙️ 配置文件标准化
```bash
# 环境配置文件命名规范
.env.backend.dev      # 开发环境
.env.backend.test     # 测试环境  
.env.backend.prod     # 生产环境
```

#### 🚀 部署命令标准化
```bash
# 开发环境
./deploy-backend.sh dev sqlite

# 测试环境
./deploy-backend.sh test sqlite

# 生产环境
./deploy-backend.sh prod postgresql
```

### 2. zentao-token-web (前端管理界面)

#### 🎯 推荐部署策略
- **开发环境**: Vite开发服务器 + 热重载
- **测试环境**: 静态构建 + Nginx容器
- **生产环境**: CDN + Nginx + HTTPS

#### ⚙️ 配置文件标准化
```bash
# 环境配置文件
.env.frontend.dev     # 开发环境API配置
.env.frontend.test    # 测试环境API配置
.env.frontend.prod    # 生产环境API配置
```

#### 🚀 部署命令标准化
```bash
# 开发环境
./deploy-frontend.sh dev

# 测试环境
./deploy-frontend.sh test

# 生产环境
./deploy-frontend.sh prod
```

### 3. zentao-mcp-client (MCP客户端)

#### 🎯 推荐部署策略
- **开发环境**: 源码运行 + 配置文件
- **测试环境**: Python包安装 + 配置模板
- **生产环境**: 可执行文件 + 系统服务

#### ⚙️ 配置文件标准化
```bash
# 配置文件位置标准化
~/.zentao_mcp_client/config.ini           # 用户配置
/etc/zentao_mcp_client/config.ini         # 系统配置
./config/client.dev.ini                   # 开发配置
./config/client.prod.ini                  # 生产配置
```

#### 🚀 部署命令标准化
```bash
# 开发环境
./deploy-client.sh dev

# 生产环境
./deploy-client.sh prod --install-service
```

### 4. OldMCPService (旧版服务)

#### 🎯 推荐部署策略
- **维护模式**: 仅保持基本运行
- **迁移计划**: 逐步迁移到新架构
- **兼容性**: 保持API兼容性

#### ⚙️ 配置文件标准化
```bash
# 简化配置
.env.legacy.dev       # 开发环境
.env.legacy.prod      # 生产环境（最小配置）
```

## 🎯 详细环境配置推荐方案

### 开发环境配置策略

#### 数据库选择
- **推荐**: SQLite（快速启动，无外部依赖）
- **配置**: 本地文件存储，开发友好

#### 服务启动方式
- **后端**: 直接Python运行（支持热重载）
- **前端**: Vite开发服务器
- **客户端**: 源码调试模式

#### 配置文件模板
```bash
# .env.dev.template
DATABASE_URL=sqlite:///./data/zentao_mcp_dev.db
DEBUG=true
LOG_LEVEL=DEBUG
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
SECRET_KEY=dev-secret-key-not-for-production
```

### 测试环境配置策略

#### 数据库选择
- **推荐**: SQLite（快速部署，易于重置）
- **配置**: 容器化存储，自动备份

#### 服务启动方式
- **后端**: Docker容器（模拟生产环境）
- **前端**: 静态构建 + Nginx
- **客户端**: 包安装模式

#### 配置文件模板
```bash
# .env.test.template
DATABASE_URL=sqlite:///./data/zentao_mcp_test.db
DEBUG=false
LOG_LEVEL=INFO
CORS_ORIGINS=http://test.example.com
SECRET_KEY=test-secret-key-change-me
```

### 生产环境配置策略

#### 数据库选择
- **推荐**: PostgreSQL（高性能，高可用）
- **配置**: 集群部署，自动备份

#### 服务启动方式
- **后端**: Docker Swarm/K8s（高可用）
- **前端**: CDN + 负载均衡
- **客户端**: 系统服务模式

#### 配置文件模板
```bash
# .env.prod.template
DATABASE_URL=******************************/zentao_mcp_prod
DEBUG=false
LOG_LEVEL=WARNING
CORS_ORIGINS=https://yourdomain.com
SECRET_KEY=${SECRET_KEY_FROM_VAULT}
```

## 🔧 配置管理工具推荐

### 1. 环境变量管理
- **开发**: .env文件 + python-dotenv
- **生产**: Docker secrets + 环境变量注入

### 2. 配置验证
- **工具**: Pydantic Settings
- **验证**: 启动时配置检查

### 3. 密钥管理
- **开发**: 本地密钥文件
- **生产**: HashiCorp Vault / AWS Secrets Manager

## 📊 部署脚本统一化建议

### 脚本命名规范
```bash
deploy-backend.sh     # 后端服务部署
deploy-frontend.sh    # 前端服务部署  
deploy-client.sh      # 客户端部署
deploy-all.sh         # 全栈部署
```

### 参数标准化
```bash
# 统一参数格式
./deploy-{service}.sh {environment} [options]

# 示例
./deploy-backend.sh dev --database=sqlite
./deploy-frontend.sh prod --cdn=true
./deploy-client.sh test --config=./config/test.ini
```

## 🎯 下一步行动计划

1. **配置文件标准化** - 统一命名和格式
2. **部署脚本重构** - 实现参数标准化
3. **环境隔离优化** - 确保环境间配置隔离
4. **监控和日志** - 添加统一的监控配置
5. **文档更新** - 更新部署文档和操作手册

## 🚨 风险点识别

1. **配置泄露风险** - 生产密钥管理
2. **环境混淆风险** - 配置文件命名冲突
3. **依赖版本风险** - 不同环境依赖不一致
4. **数据迁移风险** - SQLite到PostgreSQL迁移

## 📋 详细配置方案

### 1. zentao-mcp-backend-service 详细配置方案

#### 🎯 配置文件结构
```
zentao-mcp-backend-service/
├── config/
│   ├── .env.dev.template      # 开发环境模板
│   ├── .env.test.template     # 测试环境模板
│   ├── .env.prod.template     # 生产环境模板
│   └── settings.py            # 配置验证类
├── docker/
│   ├── Dockerfile.dev         # 开发环境Docker
│   ├── Dockerfile.test        # 测试环境Docker
│   └── Dockerfile.prod        # 生产环境Docker
└── scripts/
    ├── deploy-backend.sh      # 统一部署脚本
    ├── init-dev.sh           # 开发环境初始化
    └── health-check.sh       # 健康检查脚本
```

#### ⚙️ 环境配置详情

**开发环境配置 (.env.dev.template)**
```bash
# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp_dev.db
DATABASE_ECHO=true

# 应用配置
DEBUG=true
LOG_LEVEL=DEBUG
SECRET_KEY=dev-secret-key-not-for-production-$(date +%s)

# API配置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000
API_PREFIX=/api/v1

# 禅道API配置
ZENTAO_ENV=beta
ZENTAO_TIMEOUT=30
ZENTAO_RETRY_TIMES=3

# 开发工具配置
ENABLE_DOCS=true
ENABLE_RELOAD=true
WORKERS=1
```

**测试环境配置 (.env.test.template)**
```bash
# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp_test.db
DATABASE_ECHO=false

# 应用配置
DEBUG=false
LOG_LEVEL=INFO
SECRET_KEY=test-secret-key-change-in-production

# API配置
CORS_ORIGINS=http://test.example.com
API_PREFIX=/api/v1

# 禅道API配置
ZENTAO_ENV=beta
ZENTAO_TIMEOUT=15
ZENTAO_RETRY_TIMES=2

# 测试配置
ENABLE_DOCS=true
ENABLE_RELOAD=false
WORKERS=2
```

**生产环境配置 (.env.prod.template)**
```bash
# 数据库配置
DATABASE_URL=postgresql://${DB_USER}:${DB_PASS}@${DB_HOST}:5432/${DB_NAME}
DATABASE_ECHO=false
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# 应用配置
DEBUG=false
LOG_LEVEL=WARNING
SECRET_KEY=${SECRET_KEY_FROM_VAULT}

# API配置
CORS_ORIGINS=${ALLOWED_ORIGINS}
API_PREFIX=/api/v1

# 禅道API配置
ZENTAO_ENV=online
ZENTAO_TIMEOUT=10
ZENTAO_RETRY_TIMES=1

# 生产配置
ENABLE_DOCS=false
ENABLE_RELOAD=false
WORKERS=4
```

#### 🚀 部署脚本 (deploy-backend.sh)
```bash
#!/bin/bash
set -e

ENVIRONMENT=${1:-dev}
DATABASE=${2:-sqlite}
ACTION=${3:-deploy}

case $ENVIRONMENT in
    dev)
        ENV_FILE=".env.dev"
        COMPOSE_FILE="docker-compose.dev.yml"
        ;;
    test)
        ENV_FILE=".env.test"
        COMPOSE_FILE="docker-compose.test.yml"
        ;;
    prod)
        ENV_FILE=".env.prod"
        COMPOSE_FILE="docker-compose.prod.yml"
        ;;
esac

case $ACTION in
    deploy)
        echo "🚀 部署 $ENVIRONMENT 环境 (数据库: $DATABASE)"
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d
        ;;
    build)
        echo "🔨 构建 $ENVIRONMENT 环境镜像"
        docker-compose -f $COMPOSE_FILE build
        ;;
    stop)
        echo "⏹️ 停止 $ENVIRONMENT 环境服务"
        docker-compose -f $COMPOSE_FILE down
        ;;
esac
```

### 2. zentao-token-web 详细配置方案

#### 🎯 配置文件结构
```
zentao-token-web/
├── config/
│   ├── .env.dev               # 开发环境配置
│   ├── .env.test              # 测试环境配置
│   ├── .env.prod              # 生产环境配置
│   └── api.config.ts          # API配置管理
├── docker/
│   ├── Dockerfile.dev         # 开发环境Docker
│   ├── Dockerfile.prod        # 生产环境Docker
│   └── nginx.conf.template    # Nginx配置模板
└── scripts/
    ├── deploy-frontend.sh     # 统一部署脚本
    ├── build.sh              # 构建脚本
    └── test-api.sh           # API连接测试
```

#### ⚙️ 环境配置详情

**开发环境配置 (.env.dev)**
```bash
# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_VERSION=v1
VITE_API_TIMEOUT=10000

# 开发配置
VITE_APP_ENV=development
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true

# 调试配置
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE=true
```

**测试环境配置 (.env.test)**
```bash
# API配置
VITE_API_BASE_URL=http://test-api.example.com
VITE_API_VERSION=v1
VITE_API_TIMEOUT=15000

# 测试配置
VITE_APP_ENV=testing
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 调试配置
VITE_LOG_LEVEL=info
VITE_ENABLE_CONSOLE=false
```

**生产环境配置 (.env.prod)**
```bash
# API配置
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_API_VERSION=v1
VITE_API_TIMEOUT=20000

# 生产配置
VITE_APP_ENV=production
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 性能配置
VITE_LOG_LEVEL=error
VITE_ENABLE_CONSOLE=false
VITE_ENABLE_PWA=true
```

### 3. zentao-mcp-client 详细配置方案

#### 🎯 配置文件结构
```
zentao-mcp-client/
├── config/
│   ├── client.dev.ini         # 开发环境配置
│   ├── client.test.ini        # 测试环境配置
│   ├── client.prod.ini        # 生产环境配置
│   └── config_validator.py    # 配置验证器
├── scripts/
│   ├── deploy-client.sh       # 统一部署脚本
│   ├── install-service.sh     # 系统服务安装
│   └── test-connection.sh     # 连接测试脚本
└── systemd/
    └── zentao-mcp-client.service  # 系统服务配置
```

#### ⚙️ 环境配置详情

**开发环境配置 (client.dev.ini)**
```ini
[client]
backend_url = http://localhost:8000
api_key = dev-api-key-for-testing
timeout = 30
retry_times = 3
log_level = DEBUG

[cache]
enable_cache = true
cache_ttl = 300
cache_size = 100

[debug]
enable_debug = true
debug_file = ./logs/client_debug.log
```

**测试环境配置 (client.test.ini)**
```ini
[client]
backend_url = http://test-api.example.com
api_key = ${TEST_API_KEY}
timeout = 20
retry_times = 2
log_level = INFO

[cache]
enable_cache = true
cache_ttl = 600
cache_size = 200

[debug]
enable_debug = false
debug_file = /var/log/zentao-mcp-client/test.log
```

**生产环境配置 (client.prod.ini)**
```ini
[client]
backend_url = https://api.yourdomain.com
api_key = ${PROD_API_KEY}
timeout = 15
retry_times = 1
log_level = WARNING

[cache]
enable_cache = true
cache_ttl = 1800
cache_size = 500

[security]
verify_ssl = true
cert_file = /etc/ssl/certs/zentao-mcp.crt
```
