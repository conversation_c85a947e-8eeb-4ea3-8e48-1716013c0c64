#!/bin/bash
set -euo pipefail

# ============================================================================
# Zentao MCP Backend 部署脚本测试工具
# 测试 deploy-backend-universal.sh 脚本的各项功能
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_SCRIPT="$SCRIPT_DIR/deploy-backend-universal.sh"
TEST_LOG="$SCRIPT_DIR/deployment-test.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 日志函数
log_test() {
    echo -e "${BLUE}[TEST]${NC} $1" | tee -a "$TEST_LOG"
}

log_pass() {
    echo -e "${GREEN}[PASS]${NC} $1" | tee -a "$TEST_LOG"
    ((TESTS_PASSED++))
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1" | tee -a "$TEST_LOG"
    ((TESTS_FAILED++))
}

log_info() {
    echo -e "${YELLOW}[INFO]${NC} $1" | tee -a "$TEST_LOG"
}

# 开始测试
start_test() {
    local test_name="$1"
    ((TESTS_TOTAL++))
    log_test "开始测试: $test_name"
}

# 执行命令并检查结果
run_command() {
    local cmd="$1"
    local expected_exit_code="${2:-0}"
    local description="$3"
    
    log_info "执行命令: $cmd"
    
    if eval "$cmd" >> "$TEST_LOG" 2>&1; then
        local actual_exit_code=0
    else
        local actual_exit_code=$?
    fi
    
    if [[ $actual_exit_code -eq $expected_exit_code ]]; then
        log_pass "$description"
        return 0
    else
        log_fail "$description (期望退出码: $expected_exit_code, 实际: $actual_exit_code)"
        return 1
    fi
}

# 检查文件是否存在
check_file_exists() {
    local file_path="$1"
    local description="$2"
    
    if [[ -f "$file_path" ]]; then
        log_pass "$description"
        return 0
    else
        log_fail "$description"
        return 1
    fi
}

# 检查目录是否存在
check_dir_exists() {
    local dir_path="$1"
    local description="$2"
    
    if [[ -d "$dir_path" ]]; then
        log_pass "$description"
        return 0
    else
        log_fail "$description"
        return 1
    fi
}

# 测试1: 检查部署脚本是否存在且可执行
test_script_exists() {
    start_test "检查部署脚本存在性和可执行性"
    
    check_file_exists "$DEPLOY_SCRIPT" "部署脚本文件存在"
    
    if [[ -x "$DEPLOY_SCRIPT" ]]; then
        log_pass "部署脚本具有执行权限"
    else
        log_fail "部署脚本缺少执行权限"
    fi
}

# 测试2: 测试帮助信息
test_help_command() {
    start_test "测试帮助命令"
    
    run_command "$DEPLOY_SCRIPT --help" 0 "帮助命令执行成功"
}

# 测试3: 测试无效参数处理
test_invalid_parameters() {
    start_test "测试无效参数处理"
    
    run_command "$DEPLOY_SCRIPT invalid_env" 1 "无效环境参数被正确拒绝"
    run_command "$DEPLOY_SCRIPT dev invalid_action" 1 "无效动作参数被正确拒绝"
    run_command "$DEPLOY_SCRIPT --invalid-option" 1 "无效选项被正确拒绝"
}

# 测试4: 测试容器引擎检测
test_container_engine_detection() {
    start_test "测试容器引擎检测"
    
    # 检查Docker是否可用
    if command -v docker &> /dev/null; then
        log_info "检测到Docker"
        run_command "$DEPLOY_SCRIPT dev build --engine=docker" 0 "Docker引擎指定成功" || true
    fi
    
    # 检查Podman是否可用
    if command -v podman &> /dev/null; then
        log_info "检测到Podman"
        run_command "$DEPLOY_SCRIPT dev build --engine=podman" 0 "Podman引擎指定成功" || true
    fi
}

# 测试5: 测试配置文件创建
test_config_file_creation() {
    start_test "测试配置文件创建"
    
    # 临时移除配置文件（如果存在）
    local config_dir="zentao-mcp-backend-service/config"
    local backup_dir="${config_dir}.backup.$(date +%s)"
    
    if [[ -d "$config_dir" ]]; then
        mv "$config_dir" "$backup_dir"
        log_info "备份现有配置目录到: $backup_dir"
    fi
    
    # 测试配置文件创建
    run_command "$DEPLOY_SCRIPT dev build --verbose" 0 "开发环境配置文件创建" || true
    
    # 检查创建的文件
    check_dir_exists "$config_dir" "配置目录创建成功"
    check_file_exists "$config_dir/environments/dev.env" "开发环境配置文件创建"
    check_file_exists "$config_dir/compose/docker-compose.dev.yml" "开发环境Compose文件创建"
    check_file_exists "$config_dir/docker/Dockerfile.dev" "开发环境Dockerfile创建"
    
    # 恢复备份（如果存在）
    if [[ -d "$backup_dir" ]]; then
        rm -rf "$config_dir"
        mv "$backup_dir" "$config_dir"
        log_info "恢复原有配置目录"
    fi
}

# 测试6: 测试环境验证
test_environment_validation() {
    start_test "测试环境验证"
    
    # 测试所有支持的环境
    for env in dev test prod; do
        run_command "$DEPLOY_SCRIPT $env status" 0 "$env 环境验证通过" || true
    done
}

# 测试7: 测试详细输出模式
test_verbose_mode() {
    start_test "测试详细输出模式"
    
    run_command "$DEPLOY_SCRIPT dev status --verbose" 0 "详细输出模式工作正常" || true
}

# 测试8: 测试构建选项
test_build_options() {
    start_test "测试构建选项"
    
    # 测试无缓存构建
    run_command "$DEPLOY_SCRIPT dev build --no-cache --verbose" 0 "无缓存构建选项工作正常" || true
    
    # 测试拉取最新镜像
    run_command "$DEPLOY_SCRIPT dev build --pull --verbose" 0 "拉取最新镜像选项工作正常" || true
}

# 测试9: 测试服务管理命令
test_service_management() {
    start_test "测试服务管理命令"
    
    # 注意：这些测试不会实际启动服务，只测试命令解析
    run_command "$DEPLOY_SCRIPT dev status" 0 "状态查询命令解析正确" || true
    run_command "$DEPLOY_SCRIPT dev health" 0 "健康检查命令解析正确" || true
}

# 测试10: 测试配置文件内容
test_config_content() {
    start_test "测试配置文件内容"
    
    local config_dir="zentao-mcp-backend-service/config"
    
    # 如果配置文件存在，检查内容
    if [[ -f "$config_dir/environments/dev.env" ]]; then
        if grep -q "ENVIRONMENT=development" "$config_dir/environments/dev.env"; then
            log_pass "开发环境配置内容正确"
        else
            log_fail "开发环境配置内容不正确"
        fi
    fi
    
    if [[ -f "$config_dir/environments/prod.env" ]]; then
        if grep -q "ENVIRONMENT=production" "$config_dir/environments/prod.env"; then
            log_pass "生产环境配置内容正确"
        else
            log_fail "生产环境配置内容不正确"
        fi
    fi
}

# 清理测试环境
cleanup_test_environment() {
    log_info "清理测试环境..."
    
    # 这里可以添加清理逻辑
    # 例如：停止测试容器、删除测试文件等
    
    log_info "测试环境清理完成"
}

# 显示测试结果
show_test_results() {
    echo ""
    echo "============================================"
    echo "测试结果汇总"
    echo "============================================"
    echo "总测试数: $TESTS_TOTAL"
    echo "通过: $TESTS_PASSED"
    echo "失败: $TESTS_FAILED"
    echo "成功率: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
    echo ""
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}所有测试通过！${NC}"
        return 0
    else
        echo -e "${RED}有 $TESTS_FAILED 个测试失败${NC}"
        echo "详细日志请查看: $TEST_LOG"
        return 1
    fi
}

# 主函数
main() {
    echo "============================================"
    echo "Zentao MCP Backend 部署脚本测试"
    echo "============================================"
    echo "测试日志: $TEST_LOG"
    echo ""
    
    # 清空测试日志
    > "$TEST_LOG"
    
    # 执行所有测试
    test_script_exists
    test_help_command
    test_invalid_parameters
    test_container_engine_detection
    test_config_file_creation
    test_environment_validation
    test_verbose_mode
    test_build_options
    test_service_management
    test_config_content
    
    # 清理测试环境
    cleanup_test_environment
    
    # 显示测试结果
    show_test_results
}

# 错误处理
trap 'log_fail "测试脚本执行异常"' ERR

# 执行主函数
main "$@"
